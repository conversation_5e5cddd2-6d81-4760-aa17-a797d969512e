[{"content": "Remove all unnecessary transitions and sliding effects", "status": "completed", "priority": "high", "id": "1"}, {"content": "Set up text to reveal: 'Your toughest opponent' → 'is the one in the' → 'mirror'", "status": "completed", "priority": "high", "id": "2"}, {"content": "Make the split effect happen right after 'mirror' appears", "status": "completed", "priority": "high", "id": "3"}]