[{"content": "Explore Convex reference library for better-auth examples", "status": "completed", "priority": "high", "id": "1"}, {"content": "Implement better-auth in Convex backend", "status": "completed", "priority": "high", "id": "2"}, {"content": "Set up authentication functions and schema", "status": "completed", "priority": "high", "id": "3"}, {"content": "Configure better-auth providers", "status": "completed", "priority": "medium", "id": "4"}, {"content": "Create auth middleware and helpers", "status": "completed", "priority": "medium", "id": "5"}, {"content": "Test authentication flow", "status": "completed", "priority": "medium", "id": "6"}]