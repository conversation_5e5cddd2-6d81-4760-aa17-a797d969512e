[{"content": "Set up missing environment variables (<PERSON><PERSON>, OAuth, real Convex URL)", "status": "pending", "priority": "high", "id": "env-setup"}, {"content": "Complete Novu notifications system configuration and integration", "status": "pending", "priority": "high", "id": "novu-config"}, {"content": "Unify authentication flows and remove conflicting auth systems", "status": "pending", "priority": "high", "id": "auth-unify"}, {"content": "Replace Convex placeholder URLs with real deployment configuration", "status": "pending", "priority": "high", "id": "convex-real"}, {"content": "Test and configure Android/iOS native builds", "status": "pending", "priority": "medium", "id": "native-builds"}, {"content": "Replace mock data with real API integrations throughout the app", "status": "pending", "priority": "medium", "id": "mock-data"}, {"content": "Complete guest invitation and calendar integration features", "status": "pending", "priority": "medium", "id": "incomplete-features"}, {"content": "Set up error reporting service (Sentry/LogRocket/Bugsnag)", "status": "pending", "priority": "medium", "id": "error-monitoring"}, {"content": "Implement real-time collaboration features using Jazz framework", "status": "pending", "priority": "low", "id": "jazz-features"}, {"content": "Implement comprehensive test suite with proper coverage", "status": "pending", "priority": "low", "id": "testing-suite"}]