[{"content": "Create new Next.js app with alias-stack-convex template", "status": "in_progress", "priority": "high", "id": "1"}, {"content": "Set up Convex backend schema for HTML file metadata", "status": "pending", "priority": "high", "id": "2"}, {"content": "Create file scanning utility to index HTML files", "status": "pending", "priority": "high", "id": "3"}, {"content": "Build UI components for file grid/list view", "status": "pending", "priority": "medium", "id": "4"}, {"content": "Implement iframe viewer with navigation", "status": "pending", "priority": "medium", "id": "5"}, {"content": "Add search and filtering functionality", "status": "pending", "priority": "medium", "id": "6"}, {"content": "Set up file serving and preview generation", "status": "pending", "priority": "medium", "id": "7"}]